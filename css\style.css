/*
* HƯƠNG CAFE - STYLE.CSS
* Stylesheet chính cho trang web Hương Cafe
* Thiết kế hiện đại với màu sắc trầm ấm
*/

/* ===== BIẾN CSS ===== */
:root {
  /* <PERSON><PERSON><PERSON> sắc chính */
  --primary-color: #6f4e37; /* <PERSON>àu nâu cà phê đậm */
  --secondary-color: #b85c38; /* Màu nâu đỏ */
  --accent-color: #e09132; /* Màu cam vàng */
  --text-color: #333333; /* <PERSON>àu chữ chính */
  --text-light: #777777; /* <PERSON><PERSON>u chữ nhạt */
  --bg-color: #fff8f0; /* <PERSON><PERSON>u nền chính */
  --bg-dark: #f5e6d8; /* <PERSON><PERSON><PERSON> nền tối */
  --white: #ffffff; /* Màu trắng */
  --black: #000000; /* Màu đen */

  /* Font chữ */
  --heading-font: "Playfair Display", serif;
  --body-font: "Roboto", sans-serif;

  /* <PERSON><PERSON><PERSON> thước */
  --container-width: 1200px;
  --section-spacing: 100px;
}

/* ===== RESET CSS ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--body-font);
  color: var(--text-color);
  background-color: var(--bg-color);
  line-height: 1.6;
  overflow-x: hidden;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button,
.btn {
  cursor: pointer;
  font-family: var(--body-font);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-family: var(--heading-font);
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.divider {
  height: 3px;
  width: 60px;
  background-color: var(--accent-color);
  margin: 0 auto;
}

.btn {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--white);
  padding: 12px 30px;
  border-radius: 30px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn:hover {
  background-color: var(--secondary-color);
  transform: translateY(-3px);
}

/* ===== NAVBAR ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  font-family: var(--heading-font);
  font-size: 1.8rem;
  color: var(--primary-color);
}

.nav-links {
  display: flex;
  gap: 30px;
}

.nav-links a {
  font-weight: 500;
  position: relative;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--secondary-color);
}

.nav-links a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--secondary-color);
  transition: width 0.3s ease;
}

.nav-links a:hover::after {
  width: 100%;
}

.auth-buttons {
  display: flex;
  align-items: center;
  gap: 15px;
}

.btn-login,
.btn-signup,
.btn-logout {
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-login {
  color: var(--primary-color);
}

.btn-signup {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 8px 15px;
  border-radius: 20px;
}

.btn-logout {
  color: var(--secondary-color);
}

.user-info {
  font-size: 0.9rem;
  margin-right: 15px;
}

/* Giỏ hàng trong navbar */
.cart-button {
  position: relative;
  margin-left: 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.cart-button:hover {
  color: var(--secondary-color);
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--secondary-color);
  color: var(--white);
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.hamburger {
  display: none;
  cursor: pointer;
}

.bar {
  display: block;
  width: 25px;
  height: 3px;
  margin: 5px auto;
  background-color: var(--primary-color);
  transition: all 0.3s ease;
}

/* ===== BANNER ===== */
.banner {
  height: 100vh;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    url("../images/banner.jpg");
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--white);
  padding: 0 20px;
  margin-bottom: var(--section-spacing);
}

.banner-content {
  max-width: 800px;
}

.banner h2 {
  font-family: var(--heading-font);
  font-size: 3.5rem;
  margin-bottom: 20px;
}

.banner p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: 0 0 var(--section-spacing);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
  align-items: center;
}

.about-image img {
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-text h3 {
  font-family: var(--heading-font);
  font-size: 2rem;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.about-text p {
  margin-bottom: 20px;
  color: var(--text-light);
}

.about-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 30px;
}

.feature {
  text-align: center;
}

.feature i {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 15px;
}

.feature h4 {
  font-weight: 500;
}

/* ===== PRODUCTS SECTION ===== */
.products {
  padding: 0 0 var(--section-spacing);
  background-color: var(--bg-dark);
}

/* ===== DESSERTS SECTION ===== */
.desserts {
  padding: 0 0 var(--section-spacing);
  background-color: var(--white);
}

/* ===== COMBOS SECTION ===== */
.combos {
  padding: 0 0 var(--section-spacing);
  background-color: var(--bg-dark);
}

.original-price {
  text-decoration: line-through;
  color: var(--text-light);
  font-size: 0.9rem;
  margin-left: 5px;
}

/* ===== PAGE BANNER ===== */
.page-banner {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 100px 0 50px;
  text-align: center;
  margin-bottom: 50px;
}

.page-banner h1 {
  font-family: var(--heading-font);
  font-size: 3rem;
  margin-bottom: 15px;
}

.page-banner .divider {
  background-color: var(--white);
  margin-bottom: 20px;
}

.page-banner p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
}

/* ===== CATEGORY TABS ===== */
.categories {
  margin-bottom: 50px;
}

.category-tabs {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

.category-tab {
  background: none;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  padding: 10px 20px;
  border-radius: 30px;
  font-family: var(--body-font);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-tab.active,
.category-tab:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

/* ===== PRODUCTS PAGE ===== */
.products-page {
  padding: 0 0 var(--section-spacing);
}

.products-page .product-grid {
  margin-bottom: 50px;
}

.add-to-cart {
  display: block;
  width: 100%;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 10px;
  border-radius: 5px;
  margin-top: 15px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.add-to-cart:hover {
  background-color: var(--secondary-color);
}

/* Active link in navbar */
.nav-links a.active {
  color: var(--secondary-color);
}

.nav-links a.active::after {
  width: 100%;
}

/* ===== CART PAGE ===== */
.cart-section {
  padding: 0 0 var(--section-spacing);
}

.cart-container {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.cart-header {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr 0.5fr;
  padding: 20px;
  background-color: var(--bg-dark);
  font-weight: 500;
  text-align: center;
}

.cart-header-item:first-child {
  text-align: left;
}

.cart-item {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr 0.5fr;
  padding: 20px;
  border-bottom: 1px solid var(--bg-dark);
  align-items: center;
  text-align: center;
}

.cart-product-info {
  display: flex;
  align-items: center;
  text-align: left;
}

.cart-product-info img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 5px;
  margin-right: 15px;
}

.product-details h3 {
  font-family: var(--heading-font);
  font-size: 1.1rem;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.cart-product-price,
.cart-product-subtotal {
  font-weight: 500;
  color: var(--secondary-color);
}

.cart-product-quantity {
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn {
  width: 30px;
  height: 30px;
  background-color: var(--bg-dark);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.quantity-input {
  width: 40px;
  height: 30px;
  text-align: center;
  border: 1px solid var(--bg-dark);
  border-radius: 5px;
  margin: 0 10px;
}

.remove-btn {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  transition: color 0.3s ease;
}

.remove-btn:hover {
  color: var(--secondary-color);
}

.cart-footer {
  padding: 20px;
  background-color: var(--bg-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-total {
  display: flex;
  align-items: center;
  gap: 15px;
}

.total-label {
  font-weight: 500;
}

.total-amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--secondary-color);
}

.cart-actions {
  display: flex;
  gap: 15px;
}

.btn-secondary {
  display: inline-block;
  background-color: var(--white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 10px 25px;
  border-radius: 30px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.empty-cart-message {
  text-align: center;
  padding: 50px 20px;
}

.empty-cart-message i {
  font-size: 3rem;
  color: var(--text-light);
  margin-bottom: 20px;
}

.empty-cart-message p {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 20px;
}

/* Notification */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--primary-color);
  color: var(--white);
  padding: 15px 25px;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1001;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.product-card {
  background-color: var(--white);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.1);
}

.product-info {
  padding: 20px;
}

.product-info h3 {
  font-family: var(--heading-font);
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.product-info p {
  color: var(--text-light);
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.product-price {
  font-weight: 700;
  color: var(--secondary-color);
  font-size: 1.1rem;
}

.view-more {
  text-align: center;
  margin-top: 50px;
}

/* ===== REVIEWS SECTION ===== */
.reviews {
  padding: 0 0 var(--section-spacing);
}

.review-slider {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.review-card {
  background-color: var(--white);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.review-stars {
  color: var(--accent-color);
  margin-bottom: 15px;
}

.review-text {
  font-style: italic;
  color: var(--text-light);
  margin-bottom: 20px;
}

.reviewer {
  display: flex;
  align-items: center;
}

.reviewer img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
}

.reviewer-info h4 {
  font-weight: 500;
  margin-bottom: 5px;
}

.reviewer-info p {
  font-size: 0.9rem;
  color: var(--text-light);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: 0 0 var(--section-spacing);
  background-color: var(--bg-dark);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
}

.contact-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-right: 15px;
  margin-top: 5px;
}

.contact-item h3 {
  font-family: var(--heading-font);
  margin-bottom: 5px;
  font-size: 1.2rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: var(--body-font);
  font-size: 1rem;
}

.form-group textarea {
  height: 150px;
  resize: none;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 70px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 50px;
}

.footer-logo h2 {
  font-family: var(--heading-font);
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.footer-links h3,
.footer-social h3,
.footer-newsletter h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
  font-weight: 500;
}

.footer-links ul li {
  margin-bottom: 10px;
}

.footer-links ul li a:hover {
  color: var(--accent-color);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-icons a:hover {
  background-color: var(--accent-color);
  transform: translateY(-3px);
}

.footer-newsletter form {
  display: flex;
  margin-top: 15px;
}

.footer-newsletter input {
  flex: 1;
  padding: 10px 15px;
  border: none;
  border-radius: 5px 0 0 5px;
  font-family: var(--body-font);
}

.footer-newsletter button {
  background-color: var(--accent-color);
  color: var(--white);
  border: none;
  padding: 0 20px;
  border-radius: 0 5px 5px 0;
  transition: background-color 0.3s ease;
}

.footer-newsletter button:hover {
  background-color: var(--secondary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 992px) {
  .about-content,
  .contact-content {
    grid-template-columns: 1fr;
  }

  .about-image {
    order: -1;
  }

  .banner h2 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .navbar .container {
    position: relative;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background-color: var(--white);
    text-align: center;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding: 20px 0;
    clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
    transition: all 0.3s ease;
  }

  .nav-links.active {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  }

  .hamburger {
    display: block;
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  .about-features {
    grid-template-columns: 1fr;
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .banner h2 {
    font-size: 2rem;
  }

  .banner p {
    font-size: 1rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }

  .footer-newsletter form {
    flex-direction: column;
  }

  .footer-newsletter input {
    border-radius: 5px;
    margin-bottom: 10px;
  }

  .footer-newsletter button {
    border-radius: 5px;
    padding: 10px;
  }
}

/* ===== AUTH PAGES ===== */
.auth-container {
  max-width: 400px;
  margin: 150px auto 50px;
  padding: 30px;
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h2 {
  font-family: var(--heading-font);
  color: var(--primary-color);
  font-size: 2rem;
  margin-bottom: 10px;
}

.auth-header p {
  color: var(--text-light);
}

.auth-form .form-group {
  margin-bottom: 20px;
}

.auth-form .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.auth-form .form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: var(--body-font);
  font-size: 1rem;
}

.auth-form .btn {
  width: 100%;
  padding: 12px;
  margin-top: 10px;
}

.auth-footer {
  text-align: center;
  margin-top: 20px;
  color: var(--text-light);
}

.auth-footer a {
  color: var(--primary-color);
  font-weight: 500;
}

.auth-footer a:hover {
  text-decoration: underline;
}

.auth-error {
  background-color: #ffebee;
  color: #c62828;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: none;
}

.auth-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: none;
}

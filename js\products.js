/*
 * HƯƠNG CAFE - PRODUCTS.JS
 * JavaScript xử lý trang sản phẩm
 */

// Đ<PERSON>i cho trang web tải xong
document.addEventListener("DOMContentLoaded", function () {
  // Xử lý tab danh mục sản phẩm
  const categoryTabs = document.querySelectorAll(".category-tab");
  const productCards = document.querySelectorAll(".product-card");

  // Thêm sự kiện click cho các tab danh mục
  categoryTabs.forEach((tab) => {
    tab.addEventListener("click", function () {
      // Xóa class active khỏi tất cả các tab
      categoryTabs.forEach((t) => t.classList.remove("active"));

      // Thêm class active cho tab được click
      this.classList.add("active");

      // Lấy danh mục được chọn
      const category = this.getAttribute("data-category");

      // Hi<PERSON>n thị/ẩn sản phẩm dựa trên danh mục
      productCards.forEach((card) => {
        if (
          category === "all" ||
          card.getAttribute("data-category") === category
        ) {
          card.style.display = "block";
        } else {
          card.style.display = "none";
        }
      });
    });
  });

  // Xử lý nút thêm vào giỏ hàng
  const addToCartButtons = document.querySelectorAll(".add-to-cart");

  addToCartButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // Lấy thông tin sản phẩm
      const productCard = this.closest(".product-card");
      const productName = productCard.querySelector("h3").textContent;
      const productPrice = productCard
        .querySelector(".product-price")
        .textContent.split("đ")[0]
        .trim();
      const productImage = productCard.querySelector("img").src;

      // Thêm sản phẩm vào giỏ hàng (localStorage)
      addProductToCart(productName, productPrice, productImage);

      // Hiển thị thông báo
      showNotification(`Đã thêm ${productName} vào giỏ hàng!`);
    });
  });

  // Hàm thêm sản phẩm vào giỏ hàng
  function addProductToCart(name, price, image) {
    // Lấy giỏ hàng hiện tại từ localStorage
    let cart = JSON.parse(localStorage.getItem("cart")) || [];

    // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
    const existingProductIndex = cart.findIndex((item) => item.name === name);

    if (existingProductIndex !== -1) {
      // Nếu sản phẩm đã có trong giỏ hàng, tăng số lượng
      cart[existingProductIndex].quantity += 1;
    } else {
      // Nếu sản phẩm chưa có trong giỏ hàng, thêm mới
      cart.push({
        name: name,
        price: price,
        image: image,
        quantity: 1,
      });
    }

    // Lưu giỏ hàng vào localStorage
    localStorage.setItem("cart", JSON.stringify(cart));

    // Cập nhật số lượng sản phẩm trong giỏ hàng
    updateCartCount();
  }

  // Hàm cập nhật số lượng sản phẩm trong giỏ hàng
  function updateCartCount() {
    const cart = JSON.parse(localStorage.getItem("cart")) || [];
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);

    // Hiển thị số lượng sản phẩm trong giỏ hàng (nếu có phần tử hiển thị)
    const cartCountElement = document.getElementById("cartCount");
    if (cartCountElement) {
      cartCountElement.textContent = cartCount;

      if (cartCount > 0) {
        cartCountElement.style.display = "block";
      } else {
        cartCountElement.style.display = "none";
      }
    }
  }

  // Hàm hiển thị thông báo
  function showNotification(message) {
    // Kiểm tra xem đã có phần tử thông báo chưa
    let notification = document.querySelector(".notification");

    // Nếu chưa có, tạo mới
    if (!notification) {
      notification = document.createElement("div");
      notification.className = "notification";
      document.body.appendChild(notification);

      // Thêm CSS cho thông báo
      const style = document.createElement("style");
      style.textContent = `
                .notification {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background-color: var(--primary-color);
                    color: white;
                    padding: 15px 20px;
                    border-radius: 5px;
                    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    transform: translateY(100px);
                    opacity: 0;
                    transition: transform 0.3s, opacity 0.3s;
                }
                
                .notification.show {
                    transform: translateY(0);
                    opacity: 1;
                }
            `;
      document.head.appendChild(style);
    }

    // Hiển thị thông báo
    notification.textContent = message;
    notification.classList.add("show");

    // Ẩn thông báo sau 3 giây
    setTimeout(() => {
      notification.classList.remove("show");
    }, 3000);
  }

  // Cập nhật số lượng sản phẩm trong giỏ hàng khi trang được tải
  updateCartCount();
});

/*
 * HƯƠNG CAFE - MAIN.JS
 * JavaScript chính cho trang web Hương Cafe
 * Xử lý các tương tác và hiệu ứng
 */

// Đợ<PERSON> cho trang web tải xong
document.addEventListener("DOMContentLoaded", function () {
  // ===== MENU HAMBURGER =====
  const hamburger = document.querySelector(".hamburger");
  const navLinks = document.querySelector(".nav-links");

  // Xử lý sự kiện click vào menu hamburger
  if (hamburger) {
    hamburger.addEventListener("click", function () {
      hamburger.classList.toggle("active");
      navLinks.classList.toggle("active");
    });
  }

  // Đóng menu khi click vào các liên kết
  const navItems = document.querySelectorAll(".nav-links a");
  navItems.forEach((item) => {
    item.addEventListener("click", function () {
      hamburger.classList.remove("active");
      navLinks.classList.remove("active");
    });
  });

  // ===== NAVBAR SCROLL =====
  const navbar = document.querySelector(".navbar");

  // Thay đổi kiểu dáng navbar khi cuộn trang
  window.addEventListener("scroll", function () {
    if (window.scrollY > 50) {
      navbar.style.padding = "10px 0";
      navbar.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
    } else {
      navbar.style.padding = "15px 0";
      navbar.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
    }
  });

  // ===== HIỆU ỨNG HIỆN DẦN KHI CUỘN =====
  // Chọn tất cả các phần tử cần hiệu ứng
  const fadeElements = document.querySelectorAll(
    ".section-header, .about-content, .product-card, .review-card, .contact-content"
  );

  // Hàm kiểm tra phần tử có trong tầm nhìn không
  function checkFade() {
    fadeElements.forEach((element) => {
      const elementTop = element.getBoundingClientRect().top;
      const elementVisible = 150;

      if (elementTop < window.innerHeight - elementVisible) {
        element.classList.add("visible");
      }
    });
  }

  // Thêm class cho CSS
  fadeElements.forEach((element) => {
    element.classList.add("fade-in");
  });

  // Gọi hàm khi tải trang và khi cuộn
  window.addEventListener("scroll", checkFade);
  checkFade();

  // ===== FORM LIÊN HỆ =====
  const contactForm = document.querySelector(".contact-form form");

  if (contactForm) {
    contactForm.addEventListener("submit", function (e) {
      e.preventDefault();

      // Lấy dữ liệu từ form
      const name = contactForm.querySelector('input[type="text"]').value;
      const email = contactForm.querySelector('input[type="email"]').value;
      const phone = contactForm.querySelectorAll('input[type="text"]')[1].value;
      const message = contactForm.querySelector("textarea").value;

      // Kiểm tra dữ liệu (đơn giản)
      if (!name || !email || !message) {
        alert("Vui lòng điền đầy đủ thông tin bắt buộc!");
        return;
      }

      // Mô phỏng gửi form (trong thực tế sẽ gửi đến server)
      alert(
        "Cảm ơn bạn đã liên hệ với chúng tôi! Chúng tôi sẽ phản hồi sớm nhất có thể."
      );

      // Reset form
      contactForm.reset();
    });
  }

  // ===== FORM ĐĂNG KÝ NHẬN TIN =====
  const newsletterForm = document.querySelector(".footer-newsletter form");

  if (newsletterForm) {
    newsletterForm.addEventListener("submit", function (e) {
      e.preventDefault();

      const email = newsletterForm.querySelector('input[type="email"]').value;

      if (!email) {
        alert("Vui lòng nhập địa chỉ email của bạn!");
        return;
      }

      alert("Cảm ơn bạn đã đăng ký nhận tin từ Hương Cafe!");
      newsletterForm.reset();
    });
  }

  // ===== HIỆU ỨNG SMOOTH SCROLL =====
  // Lấy tất cả các liên kết nội bộ
  const scrollLinks = document.querySelectorAll('a[href^="#"]');

  scrollLinks.forEach((link) => {
    link.addEventListener("click", function (e) {
      // Ngăn hành vi mặc định
      e.preventDefault();

      // Lấy ID từ href
      const targetId = this.getAttribute("href");

      // Bỏ qua nếu href="#"
      if (targetId === "#") return;

      // Lấy phần tử mục tiêu
      const targetElement = document.querySelector(targetId);

      if (targetElement) {
        // Tính toán vị trí cuộn
        const navbarHeight = navbar.getBoundingClientRect().height;
        const targetPosition =
          targetElement.getBoundingClientRect().top +
          window.pageYOffset -
          navbarHeight;

        // Cuộn đến vị trí
        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    });
  });
});

// ===== CSS BỔ SUNG CHO HIỆU ỨNG =====
// Thêm CSS vào trang
const style = document.createElement("style");
style.textContent = `
    .fade-in {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.6s ease, transform 0.6s ease;
    }
    
    .fade-in.visible {
        opacity: 1;
        transform: translateY(0);
    }
    
    .product-card, .review-card {
        transition-delay: calc(0.1s * var(--i, 0));
    }
`;
document.head.appendChild(style);

// Thêm biến --i cho các phần tử để tạo hiệu ứng lần lượt
document.addEventListener("DOMContentLoaded", function () {
  const productCards = document.querySelectorAll(".product-card");
  const reviewCards = document.querySelectorAll(".review-card");

  productCards.forEach((card, index) => {
    card.style.setProperty("--i", index);
  });

  reviewCards.forEach((card, index) => {
    card.style.setProperty("--i", index);
  });
});

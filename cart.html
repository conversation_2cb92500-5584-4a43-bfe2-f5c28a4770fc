<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Giỏ Hàng - Essence Coffee</title>
    <link rel="stylesheet" href="css/style.css" />
    <!-- Font Awesome cho các biểu tượng -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Playfair+Display:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- NAVBAR -->
    <header class="navbar">
      <div class="container">
        <div class="logo">
          <a href="index.html"><h1>Essence Coffee</h1></a>
        </div>
        <nav>
          <ul class="nav-links">
            <li><a href="index.html">Trang chủ</a></li>
            <li><a href="index.html#about">Giới thiệu</a></li>
            <li><a href="drinks.html">Đồ uống</a></li>
            <li><a href="desserts.html">Bánh ngọt</a></li>
            <li><a href="combos.html">Combo</a></li>
            <li><a href="index.html#reviews">Đánh giá</a></li>
            <li><a href="index.html#contact">Liên hệ</a></li>
          </ul>
        </nav>
        <div class="auth-buttons">
          <a href="login.html" class="btn-login" id="loginBtn">Đăng nhập</a>
          <a href="signup.html" class="btn-signup" id="signupBtn">Đăng ký</a>
          <a href="#" class="btn-logout" id="logoutBtn" style="display: none"
            >Đăng xuất</a
          >
          <span class="user-info" id="userInfo" style="display: none"
            >Xin chào, <span id="userName"></span
          ></span>
        </div>
        <div class="cart-button" id="cartButton">
          <i class="fas fa-shopping-cart"></i>
          <span class="cart-count" id="cartCount">0</span>
        </div>
        <div class="hamburger">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
      </div>
    </header>

    <!-- BANNER GIỎ HÀNG -->
    <section class="page-banner">
      <div class="container">
        <h1>Giỏ Hàng</h1>
        <div class="divider"></div>
        <p>Xem lại và thanh toán các sản phẩm bạn đã chọn</p>
      </div>
    </section>

    <!-- NỘI DUNG GIỎ HÀNG -->
    <section class="cart-section">
      <div class="container">
        <div class="cart-container">
          <div class="cart-header">
            <div class="cart-header-item product-info">Sản phẩm</div>
            <div class="cart-header-item">Đơn giá</div>
            <div class="cart-header-item">Số lượng</div>
            <div class="cart-header-item">Thành tiền</div>
            <div class="cart-header-item">Xóa</div>
          </div>

          <div id="cartItems" class="cart-items">
            <!-- Các sản phẩm trong giỏ hàng sẽ được thêm vào đây bằng JavaScript -->
            <div class="empty-cart-message" id="emptyCartMessage">
              <i class="fas fa-shopping-cart"></i>
              <p>Giỏ hàng của bạn đang trống</p>
              <a href="drinks.html" class="btn">Tiếp tục mua sắm</a>
            </div>
          </div>

          <div class="cart-footer">
            <div class="cart-total">
              <div class="total-label">Tổng cộng:</div>
              <div class="total-amount" id="cartTotal">0đ</div>
            </div>
            <div class="cart-actions">
              <button id="clearCartBtn" class="btn-secondary">
                Xóa giỏ hàng
              </button>
              <button id="checkoutBtn" class="btn">Thanh toán</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FOOTER -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <h2>Essence Coffee</h2>
            <p>Không gian cà phê trầm ấm</p>
          </div>
          <div class="footer-links">
            <h3>Liên kết nhanh</h3>
            <ul>
              <li><a href="index.html">Trang chủ</a></li>
              <li><a href="index.html#about">Giới thiệu</a></li>
              <li><a href="drinks.html">Đồ uống</a></li>
              <li><a href="desserts.html">Bánh ngọt</a></li>
              <li><a href="combos.html">Combo</a></li>
              <li><a href="index.html#reviews">Đánh giá</a></li>
              <li><a href="index.html#contact">Liên hệ</a></li>
            </ul>
          </div>
          <div class="footer-social">
            <h3>Kết nối với chúng tôi</h3>
            <div class="social-icons">
              <a href="#"><i class="fab fa-facebook-f"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
              <a href="#"><i class="fab fa-twitter"></i></a>
              <a href="#"><i class="fab fa-tiktok"></i></a>
            </div>
          </div>
          <div class="footer-newsletter">
            <h3>Đăng ký nhận tin</h3>
            <p>Nhận thông tin về khuyến mãi và sản phẩm mới</p>
            <form>
              <input type="email" placeholder="Email của bạn" />
              <button type="submit">Đăng ký</button>
            </form>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2023 Essence Coffee. Tất cả các quyền được bảo lưu.</p>
        </div>
      </div>
    </footer>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>

    <!-- JavaScript chính -->
    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/cart.js"></script>

    <!-- JavaScript cho trang giỏ hàng -->
    <script>
      // Hiển thị các sản phẩm trong giỏ hàng
      function renderCartItems() {
        const cartItemsContainer = document.getElementById("cartItems");
        const emptyCartMessage = document.getElementById("emptyCartMessage");

        // Xóa nội dung hiện tại
        cartItemsContainer.innerHTML = "";

        // Kiểm tra giỏ hàng có trống không
        if (cart.length === 0) {
          cartItemsContainer.appendChild(emptyCartMessage);
          return;
        }

        // Thêm từng sản phẩm vào container
        cart.forEach((item) => {
          const cartItem = document.createElement("div");
          cartItem.className = "cart-item";
          cartItem.dataset.id = item.id;

          const subtotal =
            parseFloat(item.price.replace(/\D/g, "")) * item.quantity;

          cartItem.innerHTML = `
            <div class="cart-product-info">
              <img src="${item.image}" alt="${item.name}">
              <div class="product-details">
                <h3>${item.name}</h3>
              </div>
            </div>
            <div class="cart-product-price">${item.price}</div>
            <div class="cart-product-quantity">
              <button class="quantity-btn decrease">-</button>
              <input type="number" value="${
                item.quantity
              }" min="1" class="quantity-input">
              <button class="quantity-btn increase">+</button>
            </div>
            <div class="cart-product-subtotal">${formatCurrency(subtotal)}</div>
            <div class="cart-product-remove">
              <button class="remove-btn"><i class="fas fa-trash"></i></button>
            </div>
          `;

          cartItemsContainer.appendChild(cartItem);

          // Thêm sự kiện cho các nút
          const decreaseBtn = cartItem.querySelector(".decrease");
          const increaseBtn = cartItem.querySelector(".increase");
          const quantityInput = cartItem.querySelector(".quantity-input");
          const removeBtn = cartItem.querySelector(".remove-btn");

          decreaseBtn.addEventListener("click", () => {
            updateQuantity(item.id, item.quantity - 1);
          });

          increaseBtn.addEventListener("click", () => {
            updateQuantity(item.id, item.quantity + 1);
          });

          quantityInput.addEventListener("change", () => {
            updateQuantity(item.id, parseInt(quantityInput.value));
          });

          removeBtn.addEventListener("click", () => {
            removeFromCart(item.id);
          });
        });

        // Thêm lại thông báo giỏ hàng trống (ẩn)
        emptyCartMessage.style.display = "none";
        cartItemsContainer.appendChild(emptyCartMessage);
      }

      // Cập nhật tổng tiền giỏ hàng
      function updateCartTotal() {
        const cartTotalElement = document.getElementById("cartTotal");
        const total = calculateCartTotal();
        cartTotalElement.textContent = formatCurrency(total);
      }

      // Cập nhật thành tiền của một sản phẩm
      function updateCartItemSubtotal(productId) {
        const cartItem = document.querySelector(
          `.cart-item[data-id="${productId}"]`
        );
        if (cartItem) {
          const item = cart.find((item) => item.id === productId);
          if (item) {
            const subtotal =
              parseFloat(item.price.replace(/\D/g, "")) * item.quantity;
            cartItem.querySelector(".cart-product-subtotal").textContent =
              formatCurrency(subtotal);
            cartItem.querySelector(".quantity-input").value = item.quantity;
          }
        }
      }

      // Thêm sự kiện cho nút xóa giỏ hàng và thanh toán
      document.addEventListener("DOMContentLoaded", function () {
        const clearCartBtn = document.getElementById("clearCartBtn");
        const checkoutBtn = document.getElementById("checkoutBtn");

        if (clearCartBtn) {
          clearCartBtn.addEventListener("click", function () {
            if (
              confirm(
                "Bạn có chắc chắn muốn xóa tất cả sản phẩm trong giỏ hàng?"
              )
            ) {
              clearCart();
            }
          });
        }

        if (checkoutBtn) {
          checkoutBtn.addEventListener("click", function () {
            if (cart.length === 0) {
              alert(
                "Giỏ hàng của bạn đang trống. Vui lòng thêm sản phẩm vào giỏ hàng trước khi thanh toán."
              );
              return;
            }

            alert("Cảm ơn bạn đã mua hàng! Đơn hàng của bạn đã được xác nhận.");
            clearCart();
          });
        }
      });
    </script>
  </body>
</html>

/*
* ESSENCE COFFEE - CART.JS
* JavaScript cho chức năng giỏ hàng
*/

// Khởi tạo giỏ hàng từ localStorage hoặc tạo mới nếu chưa có
let cart = JSON.parse(localStorage.getItem('cart')) || [];
let cartCount = 0;

// Cập nhật số lượng sản phẩm trong giỏ hàng
function updateCartCount() {
    cartCount = cart.reduce((total, item) => total + item.quantity, 0);
    const cartCountElement = document.getElementById('cartCount');
    if (cartCountElement) {
        cartCountElement.textContent = cartCount;
        
        // Hiển thị hoặc ẩn số lượng dựa vào giỏ hàng có trống hay không
        if (cartCount > 0) {
            cartCountElement.style.display = 'flex';
        } else {
            cartCountElement.style.display = 'none';
        }
    }
}

// Thêm sản phẩm vào giỏ hàng
function addToCart(product) {
    // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
    const existingProductIndex = cart.findIndex(item => 
        item.id === product.id
    );

    if (existingProductIndex !== -1) {
        // Nếu sản phẩm đã có trong giỏ hàng, tăng số lượng
        cart[existingProductIndex].quantity += 1;
    } else {
        // Nếu sản phẩm chưa có trong giỏ hàng, thêm mới
        cart.push({
            ...product,
            quantity: 1
        });
    }

    // Lưu giỏ hàng vào localStorage
    saveCart();
    
    // Cập nhật số lượng hiển thị
    updateCartCount();
    
    // Hiển thị thông báo
    showNotification('Đã thêm sản phẩm vào giỏ hàng!');
}

// Xóa sản phẩm khỏi giỏ hàng
function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    saveCart();
    updateCartCount();
    
    // Nếu đang ở trang giỏ hàng, cập nhật giao diện
    if (window.location.pathname.includes('cart.html')) {
        renderCartItems();
        updateCartTotal();
    }
}

// Cập nhật số lượng sản phẩm trong giỏ hàng
function updateQuantity(productId, newQuantity) {
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        if (newQuantity <= 0) {
            // Nếu số lượng <= 0, xóa sản phẩm khỏi giỏ hàng
            removeFromCart(productId);
        } else {
            // Cập nhật số lượng mới
            cart[productIndex].quantity = newQuantity;
            saveCart();
            updateCartCount();
            
            // Nếu đang ở trang giỏ hàng, cập nhật giao diện
            if (window.location.pathname.includes('cart.html')) {
                updateCartItemSubtotal(productId);
                updateCartTotal();
            }
        }
    }
}

// Lưu giỏ hàng vào localStorage
function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

// Xóa toàn bộ giỏ hàng
function clearCart() {
    cart = [];
    saveCart();
    updateCartCount();
    
    // Nếu đang ở trang giỏ hàng, cập nhật giao diện
    if (window.location.pathname.includes('cart.html')) {
        renderCartItems();
        updateCartTotal();
    }
}

// Hiển thị thông báo
function showNotification(message) {
    // Kiểm tra xem đã có thông báo nào chưa
    let notification = document.querySelector('.notification');
    
    // Nếu chưa có, tạo mới
    if (!notification) {
        notification = document.createElement('div');
        notification.className = 'notification';
        document.body.appendChild(notification);
    }
    
    // Cập nhật nội dung và hiển thị
    notification.textContent = message;
    notification.classList.add('show');
    
    // Tự động ẩn sau 3 giây
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}

// Tính tổng tiền của giỏ hàng
function calculateCartTotal() {
    return cart.reduce((total, item) => {
        // Xử lý giá có định dạng "X.XXXđ"
        const price = parseFloat(item.price.replace(/\D/g, ''));
        return total + (price * item.quantity);
    }, 0);
}

// Định dạng số tiền thành chuỗi VND
function formatCurrency(amount) {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".") + 'đ';
}

// Khởi tạo khi trang được tải
document.addEventListener('DOMContentLoaded', function() {
    // Cập nhật số lượng sản phẩm trong giỏ hàng
    updateCartCount();
    
    // Thêm sự kiện cho các nút "Thêm vào giỏ"
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    
    addToCartButtons.forEach(button => {
        button.addEventListener('click', function() {
            const productCard = this.closest('.product-card');
            const productId = productCard.dataset.id || generateProductId(productCard);
            const productName = productCard.querySelector('h3').textContent;
            const productPrice = productCard.querySelector('.product-price').textContent.split(' ')[0];
            const productImage = productCard.querySelector('img').src;
            
            const product = {
                id: productId,
                name: productName,
                price: productPrice,
                image: productImage
            };
            
            addToCart(product);
        });
    });
    
    // Nếu đang ở trang giỏ hàng, hiển thị các sản phẩm
    if (window.location.pathname.includes('cart.html')) {
        renderCartItems();
        updateCartTotal();
    }
});

// Tạo ID cho sản phẩm nếu không có
function generateProductId(productElement) {
    const name = productElement.querySelector('h3').textContent;
    return 'product_' + name.toLowerCase().replace(/\s+/g, '_');
}

// Thêm sự kiện click cho nút giỏ hàng trên navbar
document.addEventListener('DOMContentLoaded', function() {
    const cartButton = document.getElementById('cartButton');
    if (cartButton) {
        cartButton.addEventListener('click', function(e) {
            window.location.href = 'cart.html';
        });
    }
});

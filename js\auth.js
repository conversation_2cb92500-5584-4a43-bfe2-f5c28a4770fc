/*
 * ESSENCE COFFEE - AUTH.JS
 * JavaScript xử lý xác thực người dùng với Firebase
 */

// Cấu hình Firebase - Thay thế bằng cấu hình thực tế của bạn
const firebaseConfig = {
  apiKey: "AIzaSyDmu2KU1pEaFmER7R4Ghx7DubT7HZUIEGY",
  authDomain: "jsi-pokemon.firebaseapp.com",
  projectId: "jsi-pokemon",
  storageBucket: "jsi-pokemon.firebasestorage.app",
  messagingSenderId: "905909703224",
  appId: "1:905909703224:web:6eee778d4c8a516426cccd",
  measurementId: "G-V0SD08V7K0",
};

// Khởi tạo Firebase
firebase.initializeApp(firebaseConfig);

// Lấy tham chiếu đến dịch vụ xác thực
const auth = firebase.auth();

// Đ<PERSON><PERSON> cho trang web tải xong
document.addEventListener("DOMContentLoaded", function () {
  // Kiểm tra trạng thái đăng nhập từ Firebase
  auth.onAuthStateChanged(function (user) {
    updateUIBasedOnAuthState(user);
  });

  // Kiểm tra nếu có thông tin người dùng trong localStorage
  const storedUser = JSON.parse(localStorage.getItem("user"));
  if (storedUser && !auth.currentUser) {
    // Hiển thị thông tin người dùng từ localStorage
    updateUIFromLocalStorage(storedUser);
  }

  // Xử lý form đăng ký
  const signupForm = document.getElementById("signupForm");
  if (signupForm) {
    signupForm.addEventListener("submit", handleSignup);
  }

  // Xử lý form đăng nhập
  const loginForm = document.getElementById("loginForm");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLogin);
  }

  // Xử lý nút đăng xuất
  const logoutBtn = document.getElementById("logoutBtn");
  if (logoutBtn) {
    logoutBtn.addEventListener("click", handleLogout);
  }
});

/**
 * Cập nhật giao diện dựa trên trạng thái đăng nhập
 * @param {Object} user - Đối tượng người dùng từ Firebase Auth
 */
function updateUIBasedOnAuthState(user) {
  const loginBtn = document.getElementById("loginBtn");
  const signupBtn = document.getElementById("signupBtn");
  const logoutBtn = document.getElementById("logoutBtn");
  const userInfo = document.getElementById("userInfo");
  const userName = document.getElementById("userName");

  if (!loginBtn || !signupBtn || !logoutBtn || !userInfo || !userName) {
    return; // Không tìm thấy các phần tử cần thiết
  }

  if (user) {
    // Người dùng đã đăng nhập
    loginBtn.style.display = "none";
    signupBtn.style.display = "none";
    logoutBtn.style.display = "inline-block";
    userInfo.style.display = "inline-block";
    userName.textContent = user.displayName || user.email;

    // Lưu thông tin người dùng vào localStorage
    localStorage.setItem(
      "user",
      JSON.stringify({
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
      })
    );
  } else {
    // Người dùng chưa đăng nhập với Firebase
    // Kiểm tra xem có thông tin người dùng trong localStorage không
    const storedUser = JSON.parse(localStorage.getItem("user"));

    // Chỉ sử dụng thông tin từ localStorage nếu người dùng không đăng xuất chủ động
    if (storedUser && !sessionStorage.getItem("userLoggedOut")) {
      // Nếu có, hiển thị thông tin từ localStorage
      updateUIFromLocalStorage(storedUser);
    } else {
      // Người dùng chưa đăng nhập hoặc đã đăng xuất
      loginBtn.style.display = "inline-block";
      signupBtn.style.display = "inline-block";
      logoutBtn.style.display = "none";
      userInfo.style.display = "none";

      // Xóa thông tin người dùng khỏi localStorage nếu có
      localStorage.removeItem("user");
    }
  }
}

/**
 * Cập nhật giao diện từ thông tin người dùng trong localStorage
 * @param {Object} storedUser - Thông tin người dùng từ localStorage
 */
function updateUIFromLocalStorage(storedUser) {
  const loginBtn = document.getElementById("loginBtn");
  const signupBtn = document.getElementById("signupBtn");
  const logoutBtn = document.getElementById("logoutBtn");
  const userInfo = document.getElementById("userInfo");
  const userName = document.getElementById("userName");

  if (!loginBtn || !signupBtn || !logoutBtn || !userInfo || !userName) {
    return; // Không tìm thấy các phần tử cần thiết
  }

  // Hiển thị thông tin người dùng
  loginBtn.style.display = "none";
  signupBtn.style.display = "none";
  logoutBtn.style.display = "inline-block";
  userInfo.style.display = "inline-block";
  userName.textContent = storedUser.displayName || storedUser.email;
}

/**
 * Xử lý đăng ký người dùng mới
 * @param {Event} e - Sự kiện submit form
 */
function handleSignup(e) {
  e.preventDefault();

  const nameInput = document.getElementById("name");
  const emailInput = document.getElementById("email");
  const passwordInput = document.getElementById("password");
  const confirmPasswordInput = document.getElementById("confirmPassword");
  const errorElement = document.getElementById("signupError");
  const successElement = document.getElementById("signupSuccess");

  // Ẩn thông báo lỗi và thành công
  errorElement.style.display = "none";
  successElement.style.display = "none";

  const name = nameInput.value.trim();
  const email = emailInput.value.trim();
  const password = passwordInput.value;
  const confirmPassword = confirmPasswordInput.value;

  // Kiểm tra mật khẩu khớp nhau
  if (password !== confirmPassword) {
    errorElement.textContent = "Mật khẩu không khớp. Vui lòng kiểm tra lại.";
    errorElement.style.display = "block";
    return;
  }

  // Kiểm tra độ dài mật khẩu
  if (password.length < 6) {
    errorElement.textContent = "Mật khẩu phải có ít nhất 6 ký tự.";
    errorElement.style.display = "block";
    return;
  }

  // Tạo tài khoản mới với Firebase
  auth
    .createUserWithEmailAndPassword(email, password)
    .then(function (userCredential) {
      // Đăng ký thành công
      const user = userCredential.user;

      // Cập nhật tên hiển thị
      return user
        .updateProfile({
          displayName: name,
        })
        .then(function () {
          // Xóa cờ đánh dấu người dùng đã đăng xuất
          sessionStorage.removeItem("userLoggedOut");

          successElement.textContent =
            "Đăng ký thành công! Đang chuyển hướng...";
          successElement.style.display = "block";

          // Chuyển hướng về trang chủ sau 2 giây
          setTimeout(function () {
            window.location.href = "index.html";
          }, 2000);
        });
    })
    .catch(function (error) {
      // Xử lý lỗi
      let errorMessage = "Đã xảy ra lỗi khi đăng ký. Vui lòng thử lại.";

      if (error.code === "auth/email-already-in-use") {
        errorMessage =
          "Email này đã được sử dụng. Vui lòng sử dụng email khác.";
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "Email không hợp lệ. Vui lòng kiểm tra lại.";
      } else if (error.code === "auth/weak-password") {
        errorMessage = "Mật khẩu quá yếu. Vui lòng chọn mật khẩu mạnh hơn.";
      }

      errorElement.textContent = errorMessage;
      errorElement.style.display = "block";
      console.error("Lỗi đăng ký:", error);
    });
}

/**
 * Xử lý đăng nhập người dùng
 * @param {Event} e - Sự kiện submit form
 */
function handleLogin(e) {
  e.preventDefault();

  const emailInput = document.getElementById("email");
  const passwordInput = document.getElementById("password");
  const errorElement = document.getElementById("loginError");
  const successElement = document.getElementById("loginSuccess");

  // Ẩn thông báo lỗi và thành công
  errorElement.style.display = "none";
  successElement.style.display = "none";

  const email = emailInput.value.trim();
  const password = passwordInput.value;

  // Đăng nhập với Firebase
  auth
    .signInWithEmailAndPassword(email, password)
    .then(function (userCredential) {
      // Đăng nhập thành công
      const user = userCredential.user;

      // Lưu thông tin người dùng vào localStorage
      localStorage.setItem(
        "user",
        JSON.stringify({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName || user.email,
        })
      );

      // Xóa cờ đánh dấu người dùng đã đăng xuất
      sessionStorage.removeItem("userLoggedOut");

      successElement.textContent = "Đăng nhập thành công! Đang chuyển hướng...";
      successElement.style.display = "block";

      // Chuyển hướng về trang chủ sau 2 giây
      setTimeout(function () {
        window.location.href = "index.html";
      }, 2000);
    })
    .catch(function (error) {
      // Xử lý lỗi
      let errorMessage =
        "Đăng nhập thất bại. Vui lòng kiểm tra lại email và mật khẩu.";

      if (error.code === "auth/user-not-found") {
        errorMessage = "Không tìm thấy tài khoản với email này.";
      } else if (error.code === "auth/wrong-password") {
        errorMessage = "Mật khẩu không chính xác.";
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "Email không hợp lệ.";
      } else if (error.code === "auth/user-disabled") {
        errorMessage = "Tài khoản này đã bị vô hiệu hóa.";
      }

      errorElement.textContent = errorMessage;
      errorElement.style.display = "block";
      console.error("Lỗi đăng nhập:", error);
    });
}

/**
 * Xử lý đăng xuất người dùng
 * @param {Event} e - Sự kiện click
 */
function handleLogout(e) {
  e.preventDefault();

  // Xóa thông tin người dùng khỏi localStorage
  localStorage.removeItem("user");

  // Đặt cờ đánh dấu người dùng đã đăng xuất chủ động
  sessionStorage.setItem("userLoggedOut", "true");

  auth
    .signOut()
    .then(function () {
      // Đăng xuất thành công
      console.log("Đăng xuất thành công");

      // Cập nhật giao diện ngay lập tức
      const loginBtn = document.getElementById("loginBtn");
      const signupBtn = document.getElementById("signupBtn");
      const logoutBtn = document.getElementById("logoutBtn");
      const userInfo = document.getElementById("userInfo");

      if (loginBtn && signupBtn && logoutBtn && userInfo) {
        loginBtn.style.display = "inline-block";
        signupBtn.style.display = "inline-block";
        logoutBtn.style.display = "none";
        userInfo.style.display = "none";
      }

      // Chuyển hướng về trang chủ
      window.location.href = "index.html";
    })
    .catch(function (error) {
      console.error("Lỗi đăng xuất:", error);
    });
}
